import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { quizzes, questions, chapters, modules, courses, quizAttempts } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/quizzes/[id] - Get a specific quiz with questions
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const quizId = parseInt(id);
    
    if (isNaN(quizId)) {
      return NextResponse.json({ error: 'Invalid quiz ID' }, { status: 400 });
    }

    // Get quiz with chapter and course information
    const quizData = await db
      .select({
        id: quizzes.id,
        name: quizzes.name,
        description: quizzes.description,
        quizType: quizzes.quizType,
        timeLimit: quizzes.timeLimit,
        minimumScore: quizzes.minimumScore,
        isActive: quizzes.isActive,
        chapterId: quizzes.chapterId,
        createdAt: quizzes.createdAt,
        updatedAt: quizzes.updatedAt,
        chapterName: chapters.name,
        moduleName: modules.name,
        courseName: courses.name,
        courseId: modules.courseId
      })
      .from(quizzes)
      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
      .leftJoin(modules, eq(chapters.moduleId, modules.id))
      .leftJoin(courses, eq(modules.courseId, courses.id))
      .where(eq(quizzes.id, quizId))
      .limit(1);

    if (quizData.length === 0) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
    }

    const quiz = quizData[0];

    // Get questions for this quiz
    const quizQuestions = await db
      .select()
      .from(questions)
      .where(eq(questions.quizId, quizId));

    // Parse options for each question
    const questionsWithParsedOptions = quizQuestions.map(question => ({
      ...question,
      options: question.options ? JSON.parse(question.options as string) : null
    }));

    return NextResponse.json({
      quiz: {
        ...quiz,
        questions: questionsWithParsedOptions
      }
    });
  } catch (error) {
    console.error('Error fetching quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/quizzes/[id] - Update a quiz
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const quizId = parseInt(id);
    
    if (isNaN(quizId)) {
      return NextResponse.json({ error: 'Invalid quiz ID' }, { status: 400 });
    }

    const body = await request.json();
    const {
      name,
      description,
      quizType,
      timeLimit,
      minimumScore,
      isActive,
      teacherId,
      questions: updatedQuestions
    } = body;

    // Check if quiz exists and verify teacher access
    const existingQuiz = await db
      .select({
        id: quizzes.id,
        chapterId: quizzes.chapterId,
        teacherId: courses.teacherId
      })
      .from(quizzes)
      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
      .leftJoin(modules, eq(chapters.moduleId, modules.id))
      .leftJoin(courses, eq(modules.courseId, courses.id))
      .where(eq(quizzes.id, quizId))
      .limit(1);

    if (existingQuiz.length === 0) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
    }

    // Verify teacher has permission to update this quiz
    if (teacherId && existingQuiz[0].teacherId !== teacherId) {
      return NextResponse.json(
        { error: 'Not authorized to update this quiz' },
        { status: 403 }
      );
    }

    // Update the quiz
    const updatedQuiz = await db
      .update(quizzes)
      .set({
        ...(name && { name }),
        ...(description && { description }),
        ...(quizType && { quizType }),
        ...(timeLimit !== undefined && { timeLimit }),
        ...(minimumScore !== undefined && { minimumScore: minimumScore.toString() }),
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date()
      })
      .where(eq(quizzes.id, quizId))
      .returning();

    // Update questions if provided
    if (updatedQuestions && Array.isArray(updatedQuestions)) {
      // Delete existing questions
      await db.delete(questions).where(eq(questions.quizId, quizId));

      // Insert updated questions
      if (updatedQuestions.length > 0) {
        const questionsToInsert = updatedQuestions.map((question: any, index: number) => ({
          quizId,
          type: question.type || 'multiple_choice',
          question: question.question,
          options: question.options ? JSON.stringify(question.options) : null,
          correctAnswer: question.correctAnswer,
          explanation: question.explanation,
          points: question.points || 1,
          orderIndex: question.orderIndex || index + 1
        }));

        await db.insert(questions).values(questionsToInsert);
      }
    }

    return NextResponse.json({
      quiz: updatedQuiz[0],
      message: 'Quiz updated successfully'
    });
  } catch (error) {
    console.error('Error updating quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/quizzes/[id] - Delete a quiz
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const quizId = parseInt(id);
    
    if (isNaN(quizId)) {
      return NextResponse.json({ error: 'Invalid quiz ID' }, { status: 400 });
    }

    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');

    // Check if quiz exists and verify teacher access
    const existingQuiz = await db
      .select({
        id: quizzes.id,
        chapterId: quizzes.chapterId,
        teacherId: courses.teacherId
      })
      .from(quizzes)
      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
      .leftJoin(modules, eq(chapters.moduleId, modules.id))
      .leftJoin(courses, eq(modules.courseId, courses.id))
      .where(eq(quizzes.id, quizId))
      .limit(1);

    if (existingQuiz.length === 0) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
    }

    // Verify teacher has permission to delete this quiz
    if (teacherId && existingQuiz[0].teacherId !== parseInt(teacherId)) {
      return NextResponse.json(
        { error: 'Not authorized to delete this quiz' },
        { status: 403 }
      );
    }

    // Delete related data in correct order
    // 1. Delete quiz attempts first
    await db.delete(quizAttempts).where(eq(quizAttempts.quizId, quizId));

    // 2. Delete questions
    await db.delete(questions).where(eq(questions.quizId, quizId));

    // 3. Finally delete the quiz
    await db.delete(quizzes).where(eq(quizzes.id, quizId));

    return NextResponse.json({ message: 'Quiz deleted successfully' });
  } catch (error) {
    console.error('Error deleting quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}