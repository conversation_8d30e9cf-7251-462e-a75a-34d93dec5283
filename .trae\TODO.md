# TODO:

- [x] add-cover-image-display: Tambahkan tampilan cover image di halaman All Classes dengan placeholder jika tidak ada image (priority: High)
- [x] update-class-interface: Update interface ClassData untuk include coverPicture field (priority: Medium)
- [x] modify-card-layout: Modifikasi layout card untuk menampilkan cover image di bagian atas (priority: Medium)
- [x] add-image-imports: Tambahkan import Next.js Image component dan icon placeholder (priority: Low)
- [ ] test-cover-image-display: Test tampilan cover image dengan dan tanpa image (**IN PROGRESS**) (priority: Low)
